import type { Ipc<PERSON>ain, WebContents, BrowserWindow } from 'electron';

import { getGlobalProxy } from '../services/storeService';
import type { ProxyStatus } from '../shared/types/electron';

import { registerAccountHandlers } from './account';
import { registerConfigHandlers } from './config';
import { registerFileHandlers } from './files';
import { registerImapFlowHandlers } from './imapFlow';
import { registerOAuthHandlers } from './oauth';
import { registerProxyHandlers } from './proxy';

type SendLogFn = (_level: 'info' | 'success' | 'error', _message: string) => void;
type SendConnectionStatusFn = (_accountId: string, _status: 'connected' | 'connecting' | 'disconnected') => void;
type SendProxyStatusFn = (_status: ProxyStatus, _details?: { ip?: string; error?: string }) => void;

export interface RegisterHandlersArgs {
  ipcMain: IpcMain;
  webContents: WebContents;
  mainWindow: BrowserWindow;
  sendLog: SendLogFn;
  sendProxyStatus: SendProxyStatusFn;
  sendConnectionStatus: SendConnectionStatusFn;
}

/**
 * @file Entry point for registering all IPC handlers.
 * It imports handlers from different files and registers them with the main process.
 */
export const registerIpcHandlers = ({ ipcMain, webContents, mainWindow, ...helpers }: RegisterHandlersArgs): void => {
    // Register all handlers from the different modules
    registerAccountHandlers(ipcMain, mainWindow, helpers.sendLog);
    registerImapFlowHandlers(ipcMain, webContents, helpers.sendLog, helpers.sendConnectionStatus);
    registerProxyHandlers(ipcMain, helpers.sendProxyStatus);
    registerConfigHandlers(ipcMain, helpers.sendLog);
    registerFileHandlers(ipcMain, helpers.sendLog);
    registerOAuthHandlers(ipcMain, helpers.sendLog);

    // Handle renderer ready signal
    ipcMain.handle('renderer:ready', async () => {
        // Initial proxy check on startup, once the renderer is ready to receive updates.
        const initialProxyConfig = await getGlobalProxy();
        if (initialProxyConfig?.enabled === true) {
            // Proxy connection test would be handled by proxy handlers
            helpers.sendProxyStatus('connecting');
        }
        helpers.sendLog('info', 'Renderer process is ready and listening for events.');
    });
};