/**
 * @file Secure token storage service for OAuth refresh tokens
 * Uses keytar for secure credential storage in the OS keychain
 */

import { app } from 'electron';

// Service name for keytar storage
const SERVICE_NAME = 'IMAPViewer';
const TOKEN_PREFIX = 'oauth_refresh_token_';

// Fallback to file-based storage if keytar is not available
let keytar: typeof import('keytar') | null = null;

// Try to import keytar, fallback to file storage if not available
try {
  keytar = require('keytar');
} catch (error) {
  console.warn('Keytar not available, using fallback storage:', error);
}

/**
 * Stores a refresh token securely for an account
 * @param accountId The account ID
 * @param refreshToken The refresh token to store
 */
export async function storeRefreshToken(accountId: string, refreshToken: string): Promise<void> {
  const key = `${TOKEN_PREFIX}${accountId}`;
  
  if (keytar) {
    try {
      await keytar.setPassword(SERVICE_NAME, key, refreshToken);
      return;
    } catch (error) {
      console.error('Failed to store token with keytar:', error);
    }
  }
  
  // Fallback to encrypted file storage
  await storeTokenInFile(accountId, refreshToken);
}

/**
 * Retrieves a refresh token for an account
 * @param accountId The account ID
 * @returns The refresh token or null if not found
 */
export async function getRefreshToken(accountId: string): Promise<string | null> {
  const key = `${TOKEN_PREFIX}${accountId}`;
  
  if (keytar) {
    try {
      return await keytar.getPassword(SERVICE_NAME, key);
    } catch (error) {
      console.error('Failed to get token with keytar:', error);
    }
  }
  
  // Fallback to file storage
  return await getTokenFromFile(accountId);
}

/**
 * Removes a refresh token for an account
 * @param accountId The account ID
 */
export async function removeRefreshToken(accountId: string): Promise<void> {
  const key = `${TOKEN_PREFIX}${accountId}`;
  
  if (keytar) {
    try {
      await keytar.deletePassword(SERVICE_NAME, key);
      return;
    } catch (error) {
      console.error('Failed to delete token with keytar:', error);
    }
  }
  
  // Fallback to file storage
  await removeTokenFromFile(accountId);
}

// Fallback file-based storage (basic encryption)
import { promises as fs } from 'fs';
import path from 'path';
import crypto from 'crypto';

const TOKENS_DIR = path.join(app.getPath('userData'), 'tokens');
const ENCRYPTION_KEY = 'imap-viewer-oauth-tokens'; // In production, use a proper key derivation

async function ensureTokensDir(): Promise<void> {
  try {
    await fs.mkdir(TOKENS_DIR, { recursive: true });
  } catch (error) {
    // Directory already exists
  }
}

function encrypt(text: string): string {
  const cipher = crypto.createCipher('aes192', ENCRYPTION_KEY);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
}

function decrypt(encryptedText: string): string {
  const decipher = crypto.createDecipher('aes192', ENCRYPTION_KEY);
  let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}

async function storeTokenInFile(accountId: string, refreshToken: string): Promise<void> {
  await ensureTokensDir();
  const filePath = path.join(TOKENS_DIR, `${accountId}.token`);
  const encrypted = encrypt(refreshToken);
  await fs.writeFile(filePath, encrypted, 'utf8');
}

async function getTokenFromFile(accountId: string): Promise<string | null> {
  try {
    const filePath = path.join(TOKENS_DIR, `${accountId}.token`);
    const encrypted = await fs.readFile(filePath, 'utf8');
    return decrypt(encrypted);
  } catch (error) {
    return null; // File doesn't exist or can't be read
  }
}

async function removeTokenFromFile(accountId: string): Promise<void> {
  try {
    const filePath = path.join(TOKENS_DIR, `${accountId}.token`);
    await fs.unlink(filePath);
  } catch (error) {
    // File doesn't exist, ignore
  }
}

/**
 * Lists all stored account IDs that have refresh tokens
 * @returns Array of account IDs
 */
export async function listStoredTokenAccounts(): Promise<string[]> {
  if (keytar) {
    try {
      const credentials = await keytar.findCredentials(SERVICE_NAME);
      return credentials
        .filter(cred => cred.account.startsWith(TOKEN_PREFIX))
        .map(cred => cred.account.replace(TOKEN_PREFIX, ''));
    } catch (error) {
      console.error('Failed to list tokens with keytar:', error);
    }
  }
  
  // Fallback to file storage
  try {
    await ensureTokensDir();
    const files = await fs.readdir(TOKENS_DIR);
    return files
      .filter(file => file.endsWith('.token'))
      .map(file => file.replace('.token', ''));
  } catch (error) {
    return [];
  }
}

/**
 * Clears all stored refresh tokens (for logout/reset)
 */
export async function clearAllTokens(): Promise<void> {
  const accountIds = await listStoredTokenAccounts();
  await Promise.all(accountIds.map(id => removeRefreshToken(id)));
}
