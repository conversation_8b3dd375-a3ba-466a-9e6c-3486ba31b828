/**
 * @file OAuth IPC handlers for secure authentication
 */

import type { IpcMain, IpcMainInvokeEvent } from 'electron';

import { startOAuthFlow, validateOAuthConfig } from '../services/oauthService';
import { checkOAuthConfiguration, isOAuthAccountReady, validateOAuthAccount } from '../services/oauthImapService';
import { 
  initializeTokenRefreshService, 
  cleanupTokenRefreshService, 
  getTokenRefreshStatus, 
  refreshAllTokensManually 
} from '../services/tokenRefreshService';
import { getRefreshToken, removeRefreshToken, listStoredTokenAccounts, clearAllTokens } from '../services/tokenStorageService';
import type { OAuthResult } from '../services/oauthService';
import type { TokenRefreshStatus } from '../services/tokenRefreshService';

type SendLogFn = (_level: 'info' | 'success' | 'error', _message: string) => void;

/**
 * Interface for OAuth flow request
 */
interface OAuthFlowRequest {
  email: string;
  clientId?: string;
}

/**
 * Interface for OAuth configuration check result
 */
interface OAuthConfigResult {
  isConfigured: boolean;
  isValid: boolean;
  issues: string[];
}

/**
 * Register OAuth authentication handlers
 */
function registerOAuthAuthHandlers(ipcMain: IpcMain, sendLog: SendLogFn): void {
  // Start OAuth authentication flow
  ipcMain.handle('oauth:start-flow', async (_event: IpcMainInvokeEvent, request: OAuthFlowRequest): Promise<OAuthResult> => {
    try {
      sendLog('info', `Starting OAuth flow for: ${request.email}`);
      
      // Validate configuration first
      if (!validateOAuthConfig()) {
        throw new Error('OAuth is not properly configured. Please check your environment variables.');
      }
      
      const result = await startOAuthFlow(request.email);
      
      sendLog('success', `OAuth authentication successful for: ${request.email}`);
      return result;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'OAuth authentication failed';
      sendLog('error', `OAuth authentication failed for ${request.email}: ${errorMessage}`);
      throw error;
    }
  });

  // Check OAuth configuration
  ipcMain.handle('oauth:check-config', async (): Promise<OAuthConfigResult> => {
    const config = checkOAuthConfiguration();
    const isValid = validateOAuthConfig();
    
    return {
      isConfigured: config.isConfigured,
      isValid,
      issues: config.issues,
    };
  });

  // Validate OAuth account configuration
  ipcMain.handle('oauth:validate-account', async (_event: IpcMainInvokeEvent, accountData: any): Promise<{ isValid: boolean; errors: string[] }> => {
    const errors = validateOAuthAccount(accountData);
    return {
      isValid: errors.length === 0,
      errors,
    };
  });

  // Check if account is ready for OAuth
  ipcMain.handle('oauth:is-account-ready', async (_event: IpcMainInvokeEvent, accountData: any): Promise<boolean> => {
    return isOAuthAccountReady(accountData);
  });
}

/**
 * Register token management handlers
 */
function registerTokenManagementHandlers(ipcMain: IpcMain, sendLog: SendLogFn): void {
  // Get refresh token for account (for debugging/admin purposes)
  ipcMain.handle('oauth:get-refresh-token', async (_event: IpcMainInvokeEvent, accountId: string): Promise<string | null> => {
    try {
      return await getRefreshToken(accountId);
    } catch (error) {
      sendLog('error', `Failed to get refresh token for account ${accountId}: ${error}`);
      return null;
    }
  });

  // Remove refresh token for account
  ipcMain.handle('oauth:remove-refresh-token', async (_event: IpcMainInvokeEvent, accountId: string): Promise<boolean> => {
    try {
      await removeRefreshToken(accountId);
      sendLog('info', `Removed refresh token for account: ${accountId}`);
      return true;
    } catch (error) {
      sendLog('error', `Failed to remove refresh token for account ${accountId}: ${error}`);
      return false;
    }
  });

  // List all accounts with stored tokens
  ipcMain.handle('oauth:list-token-accounts', async (): Promise<string[]> => {
    try {
      return await listStoredTokenAccounts();
    } catch (error) {
      sendLog('error', `Failed to list token accounts: ${error}`);
      return [];
    }
  });

  // Clear all stored tokens (for cleanup/reset)
  ipcMain.handle('oauth:clear-all-tokens', async (): Promise<boolean> => {
    try {
      await clearAllTokens();
      sendLog('info', 'Cleared all stored OAuth tokens');
      return true;
    } catch (error) {
      sendLog('error', `Failed to clear all tokens: ${error}`);
      return false;
    }
  });
}

/**
 * Register token refresh service handlers
 */
function registerTokenRefreshHandlers(ipcMain: IpcMain, sendLog: SendLogFn): void {
  // Initialize token refresh service
  ipcMain.handle('oauth:init-refresh-service', async (): Promise<boolean> => {
    try {
      initializeTokenRefreshService();
      sendLog('info', 'Token refresh service initialized');
      return true;
    } catch (error) {
      sendLog('error', `Failed to initialize token refresh service: ${error}`);
      return false;
    }
  });

  // Stop token refresh service
  ipcMain.handle('oauth:stop-refresh-service', async (): Promise<boolean> => {
    try {
      cleanupTokenRefreshService();
      sendLog('info', 'Token refresh service stopped');
      return true;
    } catch (error) {
      sendLog('error', `Failed to stop token refresh service: ${error}`);
      return false;
    }
  });

  // Get token refresh service status
  ipcMain.handle('oauth:get-refresh-status', async () => {
    try {
      return getTokenRefreshStatus();
    } catch (error) {
      sendLog('error', `Failed to get refresh status: ${error}`);
      return { isRunning: false, accountCount: 0 };
    }
  });

  // Manually refresh all tokens
  ipcMain.handle('oauth:refresh-all-tokens', async (): Promise<TokenRefreshStatus[]> => {
    try {
      sendLog('info', 'Starting manual token refresh for all accounts');
      const results = await refreshAllTokensManually();
      
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;
      
      if (failureCount === 0) {
        sendLog('success', `Successfully refreshed tokens for ${successCount} accounts`);
      } else {
        sendLog('error', `Token refresh completed: ${successCount} successful, ${failureCount} failed`);
      }
      
      return results;
    } catch (error) {
      sendLog('error', `Failed to refresh tokens: ${error}`);
      return [];
    }
  });
}

/**
 * Register OAuth debugging and utility handlers
 */
function registerOAuthUtilityHandlers(ipcMain: IpcMain, sendLog: SendLogFn): void {
  // Test OAuth configuration
  ipcMain.handle('oauth:test-config', async (): Promise<{ success: boolean; message: string }> => {
    try {
      const config = checkOAuthConfiguration();
      const isValid = validateOAuthConfig();
      
      if (!config.isConfigured) {
        return {
          success: false,
          message: `OAuth not configured: ${config.issues.join(', ')}`
        };
      }
      
      if (!isValid) {
        return {
          success: false,
          message: 'OAuth configuration is invalid'
        };
      }
      
      return {
        success: true,
        message: 'OAuth configuration is valid and ready'
      };
      
    } catch (error) {
      return {
        success: false,
        message: `Configuration test failed: ${error}`
      };
    }
  });

  // Get OAuth environment info (for debugging)
  ipcMain.handle('oauth:get-env-info', async () => {
    return {
      hasClientId: !!process.env.MICROSOFT_CLIENT_ID,
      clientIdLength: process.env.MICROSOFT_CLIENT_ID?.length || 0,
      nodeEnv: process.env.NODE_ENV,
    };
  });
}

/**
 * Main function to register all OAuth handlers
 */
export const registerOAuthHandlers = (ipcMain: IpcMain, sendLog: SendLogFn): void => {
  registerOAuthAuthHandlers(ipcMain, sendLog);
  registerTokenManagementHandlers(ipcMain, sendLog);
  registerTokenRefreshHandlers(ipcMain, sendLog);
  registerOAuthUtilityHandlers(ipcMain, sendLog);
  
  sendLog('info', 'OAuth IPC handlers registered');
};
