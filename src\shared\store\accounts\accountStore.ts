/**
 * @file Focused Zustand store for account CRUD operations
 */
import { create } from 'zustand';

import type { Account, ProxyConfig } from '../../types/account';
import type { MailBoxes } from '../../types/electron';
import type { EmailHeader } from '../../types/email';

// Extended Email type for email body content
export interface Email extends EmailHeader {
  html?: string | false;
  text?: string;
  textAsHtml?: string;
  to?: {
    text: string;
  };
}

export interface AccountState {
  accounts: Account[];
  selectedAccountId: string | null;
  globalProxy: ProxyConfig | null;

  // Compatibility fields for old store structure
  mailboxesByAccountId: Record<string, MailBoxes | null>;
  selectedMailbox: string | null;
  selectedEmailId: number | null;
  currentEmail: Email | null;
  emailHeadersByMailbox: Record<string, EmailHeader[]>;
  emailCountByMailbox: Record<string, number>;
  hasMoreEmailsByMailbox: Record<string, boolean>;

  // Account CRUD operations
  setAccounts: (accounts: Account[]) => void;
  addAccount: (account: Account) => void;
  addAccountToStore: (account: Account) => void; // Compatibility alias
  updateAccount: (accountId: string, updates: Partial<Account>) => void;
  updateAccountInStore: (accountId: string, updates: Partial<Account>) => void; // Compatibility alias
  deleteAccount: (accountId: string) => void;
  deleteAccountInStore: (accountId: string) => void; // Compatibility alias

  // Account selection
  selectAccount: (accountId: string | null) => void;

  // Proxy configuration
  setGlobalProxy: (proxy: ProxyConfig | null) => void;
  setGlobalProxyConfig: (proxy: ProxyConfig | null) => void; // Compatibility alias
  setAccountProxy: (accountId: string, proxy: ProxyConfig | null) => void;
  setAccountProxyConfig: (accountId: string, proxy: ProxyConfig | null) => void; // Compatibility alias

  // Compatibility methods for mailbox management
  setMailboxesForAccount: (accountId: string, mailboxes: MailBoxes | null) => void;
  selectMailbox: (mailboxName: string | null) => void;

  // Email selection
  selectEmail: (emailId: number | null) => void;
  setCurrentEmail: (email: Email | null) => void;

  // Compatibility methods for email management
  clearEmailHeadersForMailbox: (accountId: string, mailboxName: string) => void;
  setEmailHeadersForMailbox: (accountId: string, mailboxName: string, headers: EmailHeader[]) => void;
  appendEmailHeadersToMailbox: (accountId: string, mailboxName: string, headers: EmailHeader[]) => void;
  prependEmailHeaders: (accountId: string, mailboxName: string, headers: EmailHeader[]) => void;
  removeEmailHeaders: (uids: number[]) => void;
  setHasMoreEmailsForMailbox: (accountId: string, mailboxName: string, hasMore: boolean) => void;
  setEmailCountForMailbox: (accountId: string, mailboxName: string, count: number) => void;

  // Connection status management
  setAccountConnectionStatus: (accountId: string, status: 'connected' | 'connecting' | 'disconnected') => void;
}

export const useAccountStore = create<AccountState>((set) => ({
  accounts: [],
  selectedAccountId: null,
  globalProxy: null,

  // Compatibility fields
  mailboxesByAccountId: {},
  selectedMailbox: null,
  selectedEmailId: null,
  currentEmail: null,
  emailHeadersByMailbox: {},
  emailCountByMailbox: {},
  hasMoreEmailsByMailbox: {},

  setAccounts: (accounts: Account[]): void => set({ accounts }),

  addAccount: (account: Account): void => set((state) => ({
    accounts: [...state.accounts, { ...account, connectionStatus: 'disconnected' as const }],
  })),

  updateAccount: (accountId: string, updates: Partial<Account>): void => set((state) => ({
    accounts: state.accounts.map(acc =>
      acc.id === accountId ? { ...acc, ...updates } : acc
    ),
  })),

  deleteAccount: (accountId: string): void => set((state) => ({
    accounts: state.accounts.filter((acc) => acc.id !== accountId),
    selectedAccountId: state.selectedAccountId === accountId ? null : state.selectedAccountId,
  })),

  selectAccount: (accountId: string | null): void => set({
    selectedAccountId: accountId,
  }),

  setGlobalProxy: (proxy: ProxyConfig | null): void => set({
    globalProxy: proxy,
  }),

  setAccountProxy: (accountId: string, proxy: ProxyConfig | null): void => set((state) => ({
    accounts: state.accounts.map(acc =>
      acc.id === accountId ? { ...acc, proxy } : acc
    ),
  })),

  // Compatibility aliases for old method names
  addAccountToStore: (account: Account): void => set((state) => ({
    accounts: [...state.accounts, { ...account, connectionStatus: 'disconnected' as const }],
  })),

  updateAccountInStore: (accountId: string, updates: Partial<Account>): void => set((state) => ({
    accounts: state.accounts.map(acc =>
      acc.id === accountId ? { ...acc, ...updates } : acc
    ),
  })),

  deleteAccountInStore: (accountId: string): void => set((state) => ({
    accounts: state.accounts.filter((acc) => acc.id !== accountId),
    selectedAccountId: state.selectedAccountId === accountId ? null : state.selectedAccountId,
  })),

  setGlobalProxyConfig: (proxy: ProxyConfig | null): void => set({
    globalProxy: proxy,
  }),

  setAccountProxyConfig: (accountId: string, proxy: ProxyConfig | null): void => set((state) => ({
    accounts: state.accounts.map(acc =>
      acc.id === accountId ? { ...acc, proxy } : acc
    ),
  })),

  // Compatibility methods for mailbox management
  setMailboxesForAccount: (accountId: string, mailboxes: MailBoxes | null): void => set((state) => ({
    mailboxesByAccountId: {
      ...state.mailboxesByAccountId,
      [accountId]: mailboxes,
    },
  })),

  selectMailbox: (mailboxName: string | null): void => set({
    selectedMailbox: mailboxName,
  }),

  // Email selection
  selectEmail: (emailId: number | null): void => set({
    selectedEmailId: emailId,
  }),

  setCurrentEmail: (email: Email | null): void => set({
    currentEmail: email,
  }),

  // Compatibility methods for email management
  clearEmailHeadersForMailbox: (accountId: string, mailboxName: string): void => {
    const key = `${accountId}-${mailboxName}`;
    set((state) => {
      const newEmailHeaders = { ...state.emailHeadersByMailbox };
      delete newEmailHeaders[key];
      const newHasMore = { ...state.hasMoreEmailsByMailbox };
      delete newHasMore[key];
      return {
        emailHeadersByMailbox: newEmailHeaders,
        hasMoreEmailsByMailbox: newHasMore,
      };
    });
  },

  setEmailHeadersForMailbox: (accountId: string, mailboxName: string, headers: EmailHeader[]): void => {
    const key = `${accountId}-${mailboxName}`;
    set((state) => ({
      emailHeadersByMailbox: {
        ...state.emailHeadersByMailbox,
        [key]: headers,
      },
    }));
  },

  appendEmailHeadersToMailbox: (accountId: string, mailboxName: string, headers: EmailHeader[]): void => {
    const key = `${accountId}-${mailboxName}`;
    set((state) => {
      const existingHeaders = state.emailHeadersByMailbox[key] ?? [];
      const existingUids = new Set(existingHeaders.map(h => h.uid));
      const uniqueNewHeaders = headers.filter(h => !existingUids.has(h.uid));
      return {
        emailHeadersByMailbox: {
          ...state.emailHeadersByMailbox,
          [key]: [...existingHeaders, ...uniqueNewHeaders],
        },
      };
    });
  },

  prependEmailHeaders: (accountId: string, mailboxName: string, headers: EmailHeader[]): void => {
    const key = `${accountId}-${mailboxName}`;
    set((state) => {
      const existingHeaders = state.emailHeadersByMailbox[key] ?? [];
      const existingUids = new Set(existingHeaders.map(h => h.uid));
      const uniqueNewHeaders = headers.filter(h => !existingUids.has(h.uid));
      return {
        emailHeadersByMailbox: {
          ...state.emailHeadersByMailbox,
          [key]: [...uniqueNewHeaders, ...existingHeaders],
        },
      };
    });
  },

  removeEmailHeaders: (uids: number[]): void => {
    set((state) => {
      const updatedEmailHeaders = { ...state.emailHeadersByMailbox };
      Object.keys(updatedEmailHeaders).forEach(key => {
        updatedEmailHeaders[key] = updatedEmailHeaders[key].filter(header => !uids.includes(header.uid));
      });
      return {
        emailHeadersByMailbox: updatedEmailHeaders,
      };
    });
  },

  setHasMoreEmailsForMailbox: (accountId: string, mailboxName: string, hasMore: boolean): void => {
    const key = `${accountId}-${mailboxName}`;
    set((state) => ({
      hasMoreEmailsByMailbox: {
        ...state.hasMoreEmailsByMailbox,
        [key]: hasMore,
      },
    }));
  },

  setEmailCountForMailbox: (accountId: string, mailboxName: string, count: number): void => {
    const key = `${accountId}-${mailboxName}`;
    set((state) => ({
      emailCountByMailbox: {
        ...state.emailCountByMailbox,
        [key]: count,
      },
    }));
  },

  // Connection status management
  setAccountConnectionStatus: (accountId: string, status: 'connected' | 'connecting' | 'disconnected'): void => set((state) => ({
    accounts: state.accounts.map(acc =>
      acc.id === accountId ? { ...acc, connectionStatus: status } : acc
    ),
  })),
}));
