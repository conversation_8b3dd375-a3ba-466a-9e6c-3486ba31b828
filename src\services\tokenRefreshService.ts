/**
 * @file Background token refresh service
 * Proactively refreshes OAuth tokens before they expire
 */

import { refreshAccessToken, isTokenExpired } from './oauthService';
import { getRefreshToken, storeRefreshToken, listStoredTokenAccounts } from './tokenStorageService';
import { getAccounts } from './storeService';
import type { Account } from '../shared/types/account';

/**
 * Interface for token refresh result
 */
interface TokenRefreshStatus {
  accountId: string;
  email: string;
  success: boolean;
  error?: string;
  refreshedAt: Date;
}

/**
 * Service class for managing background token refresh
 */
class TokenRefreshService {
  private refreshInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private refreshHistory: TokenRefreshStatus[] = [];
  
  // Refresh tokens 10 minutes before they expire
  private readonly REFRESH_BUFFER_MINUTES = 10;
  
  // Check for token refresh every 5 minutes
  private readonly CHECK_INTERVAL_MS = 5 * 60 * 1000;

  /**
   * Start the background token refresh service
   */
  start(): void {
    if (this.isRunning) {
      console.log('Token refresh service is already running');
      return;
    }

    console.log('Starting token refresh service');
    this.isRunning = true;
    
    // Run initial check
    this.checkAndRefreshTokens();
    
    // Set up periodic checks
    this.refreshInterval = setInterval(() => {
      this.checkAndRefreshTokens();
    }, this.CHECK_INTERVAL_MS);
  }

  /**
   * Stop the background token refresh service
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    console.log('Stopping token refresh service');
    this.isRunning = false;
    
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }

  /**
   * Check all OAuth accounts and refresh tokens if needed
   */
  private async checkAndRefreshTokens(): Promise<void> {
    try {
      console.log('Checking OAuth tokens for refresh...');
      
      // Get all accounts
      const accounts = await getAccounts();
      const oauthAccounts = accounts.filter(account => account.authMethod === 'oauth2');
      
      if (oauthAccounts.length === 0) {
        console.log('No OAuth accounts found');
        return;
      }

      console.log(`Found ${oauthAccounts.length} OAuth accounts to check`);
      
      // Check each OAuth account
      const refreshPromises = oauthAccounts.map(account => this.refreshAccountTokenIfNeeded(account));
      const results = await Promise.allSettled(refreshPromises);
      
      // Log results
      results.forEach((result, index) => {
        const account = oauthAccounts[index];
        if (result.status === 'rejected') {
          console.error(`Failed to check token for ${account.email}:`, result.reason);
        }
      });
      
    } catch (error) {
      console.error('Error during token refresh check:', error);
    }
  }

  /**
   * Refresh token for a specific account if needed
   */
  private async refreshAccountTokenIfNeeded(account: Account): Promise<void> {
    if (!account.refreshToken) {
      console.log(`Account ${account.email} has no refresh token`);
      return;
    }

    try {
      // For now, we'll refresh proactively since we don't store access token expiry
      // In a production app, you'd want to store and check the actual expiry time
      console.log(`Refreshing token for account: ${account.email}`);
      
      const tokenResult = await refreshAccessToken(account.refreshToken, account.email);
      
      // Store new refresh token if it was rotated
      if (tokenResult.refreshToken && tokenResult.refreshToken !== account.refreshToken) {
        await storeRefreshToken(account.id, tokenResult.refreshToken);
        console.log(`Updated refresh token for account: ${account.email}`);
      }
      
      // Record successful refresh
      this.recordRefreshStatus({
        accountId: account.id,
        email: account.email,
        success: true,
        refreshedAt: new Date(),
      });
      
      console.log(`Successfully refreshed token for account: ${account.email}`);
      
    } catch (error) {
      console.error(`Failed to refresh token for account ${account.email}:`, error);
      
      // Record failed refresh
      this.recordRefreshStatus({
        accountId: account.id,
        email: account.email,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        refreshedAt: new Date(),
      });
    }
  }

  /**
   * Record token refresh status
   */
  private recordRefreshStatus(status: TokenRefreshStatus): void {
    this.refreshHistory.push(status);
    
    // Keep only last 100 entries
    if (this.refreshHistory.length > 100) {
      this.refreshHistory = this.refreshHistory.slice(-100);
    }
  }

  /**
   * Get refresh history for debugging
   */
  getRefreshHistory(): TokenRefreshStatus[] {
    return [...this.refreshHistory];
  }

  /**
   * Get service status
   */
  getStatus(): { isRunning: boolean; lastCheck?: Date; accountCount: number } {
    const lastEntry = this.refreshHistory[this.refreshHistory.length - 1];
    return {
      isRunning: this.isRunning,
      lastCheck: lastEntry?.refreshedAt,
      accountCount: this.refreshHistory.filter(entry => entry.success).length,
    };
  }

  /**
   * Manually refresh tokens for all OAuth accounts
   */
  async refreshAllTokens(): Promise<TokenRefreshStatus[]> {
    console.log('Manually refreshing all OAuth tokens...');
    
    const accounts = await getAccounts();
    const oauthAccounts = accounts.filter(account => account.authMethod === 'oauth2');
    
    const results: TokenRefreshStatus[] = [];
    
    for (const account of oauthAccounts) {
      try {
        await this.refreshAccountTokenIfNeeded(account);
        const lastStatus = this.refreshHistory.find(
          entry => entry.accountId === account.id && entry.success
        );
        if (lastStatus) {
          results.push(lastStatus);
        }
      } catch (error) {
        results.push({
          accountId: account.id,
          email: account.email,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          refreshedAt: new Date(),
        });
      }
    }
    
    return results;
  }
}

// Create singleton instance
const tokenRefreshService = new TokenRefreshService();

export { tokenRefreshService, type TokenRefreshStatus };

/**
 * Initialize token refresh service (call this from main process)
 */
export function initializeTokenRefreshService(): void {
  tokenRefreshService.start();
}

/**
 * Cleanup token refresh service (call this on app shutdown)
 */
export function cleanupTokenRefreshService(): void {
  tokenRefreshService.stop();
}

/**
 * Get token refresh service status
 */
export function getTokenRefreshStatus() {
  return tokenRefreshService.getStatus();
}

/**
 * Manually refresh all tokens
 */
export function refreshAllTokensManually() {
  return tokenRefreshService.refreshAllTokens();
}
