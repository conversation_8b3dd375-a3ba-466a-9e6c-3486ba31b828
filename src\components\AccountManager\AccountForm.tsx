/**
 * @file Refactored account form component for a better user experience.
 */

import { ArrowLeft, Save, X, RefreshCw, Eye, EyeOff, AlertCircle, Settings, Shield } from 'lucide-react';
import React, { useEffect } from 'react';
import { Controller } from 'react-hook-form';

import { FormField } from '../FormField';
import { useAccountForm } from '../../shared/hooks/useAccountForm';
import { useOAuthForm, isEmailDomainSupportedForOAuth } from '../../shared/hooks/useOAuthForm';
import { imapProviders } from '../../shared/store/imapProviders';
import type { Account } from '../../shared/types/account';
import { Button } from '../../shared/ui/button';
import { Input } from '../../shared/ui/input';
import { Label } from '../../shared/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../shared/ui/select';
import { ToggleGroup, ToggleGroupItem } from '../../shared/ui/toggle-group';
import { cn } from '../../shared/utils/utils';

// ... (interface definition remains the same)
interface AccountFormProps {
    accountToEdit?: Account | null;
    onCancel: () => void;
    onSuccess: (_data: Omit<Account, 'id'>) => Promise<void>;
    initialData?: {
      email: string;
      password: string;
    } | null;
}

const AccountForm: React.FC<AccountFormProps> = ({ accountToEdit, onCancel, onSuccess, initialData }) => {
  const {
    form,
    isPasswordVisible,
    setIsPasswordVisible,
    error,
    showProviderSuggestions,
    setShowProviderSuggestions,
    discovery,
    handleProviderSelect,
    handleManualDiscovery,
    handleEmailBlur,
    handleSubmit,
  } = useAccountForm({ accountToEdit, initialData, onSave: onSuccess });

  const {
    isOAuthInProgress,
    oauthError,
    oauthResult,
    startOAuth,
    clearOAuthError,
    isOAuthConfigured,
    oauthConfigIssues,
  } = useOAuthForm();

  const { register, formState: { errors, isSubmitting }, control, watch, setValue } = form;
  const watchedEmail = watch('email');
  const watchedAuthMethod = watch('authMethod');

  useEffect(() => {
    if (discovery.discoveryStatus === 'failed') {
      setShowProviderSuggestions(true);
    } else {
      setShowProviderSuggestions(false);
    }
  }, [discovery.discoveryStatus, setShowProviderSuggestions]);

  // Handle OAuth result
  useEffect(() => {
    if (oauthResult) {
      // Update form with OAuth data
      setValue('authMethod', 'oauth2');
      setValue('refreshToken', oauthResult.refreshToken);
      setValue('clientId', process.env.MICROSOFT_CLIENT_ID || '');

      // Clear password when using OAuth
      setValue('password', '');

      console.log('OAuth authentication completed, form updated');
    }
  }, [oauthResult, setValue]);

  return (
    <div className="h-full flex flex-col bg-background text-foreground w-full">
      {/* Header */}
      <div className="flex items-center gap-3 px-4 border-b border-border flex-shrink-0 h-14">
        <Button variant="ghost" size="icon" onClick={onCancel} className="rounded-full">
          <ArrowLeft size={20} />
        </Button>
        <h2 className="text-xl font-semibold truncate">
          {accountToEdit ? 'Edit Account' : 'Add New Account'}
        </h2>
      </div>

      {/* Form */}
      <div className="flex-grow overflow-y-auto p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Credentials */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address <span className="text-destructive ml-1">*</span></Label>
              <Input
                {...register('email')}
                type="email"
                placeholder="<EMAIL>"
                onBlur={handleEmailBlur}
                className={errors.email?.message ? "border-destructive focus-visible:ring-destructive" : ""}
              />
              {errors.email?.message && (
                <div className="flex items-center gap-2 text-sm text-destructive">
                  <AlertCircle size={16} />
                  <span>{errors.email.message}</span>
                </div>
              )}
            </div>

            {/* Authentication Method Selector */}
            <div className="space-y-2">
              <Label htmlFor="authMethod">Authentication Method</Label>
              <Controller
                control={control}
                name="authMethod"
                render={({ field }) => (
                  <ToggleGroup
                    type="single"
                    value={field.value}
                    onValueChange={field.onChange}
                    variant="outline"
                    className="w-full"
                  >
                    <ToggleGroupItem value="password" className="flex-1 flex items-center gap-2">
                      <Eye size={16} />
                      Password
                    </ToggleGroupItem>
                    <ToggleGroupItem value="oauth2" className="flex-1 flex items-center gap-2">
                      <Shield size={16} />
                      OAuth 2.0
                    </ToggleGroupItem>
                  </ToggleGroup>
                )}
              />
            </div>

            {/* Password Field - Only show for password authentication */}
            {watchedAuthMethod === 'password' && (
              <FormField
                id="password"
                label="Password"
                type="password"
                value={watch('password') || ''}
                onChange={(value) => setValue('password', value)}
                error={errors.password?.message}
                placeholder="••••••••"
                required
              />
            )}

            {/* OAuth Login Button - Only show for OAuth authentication */}
            {watchedAuthMethod === 'oauth2' && (
              <div className="space-y-3">
                {/* OAuth Configuration Warning */}
                {!isOAuthConfigured && (
                  <div className="p-4 rounded-lg bg-red-50 border border-red-200">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="text-red-600 mt-0.5" size={20} />
                      <div>
                        <h4 className="font-medium text-red-900">OAuth Not Configured</h4>
                        <p className="text-sm text-red-700 mt-1">
                          OAuth authentication is not properly configured:
                        </p>
                        <ul className="text-sm text-red-700 mt-2 list-disc list-inside">
                          {oauthConfigIssues.map((issue, index) => (
                            <li key={index}>{issue}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}

                {/* OAuth Info */}
                {isOAuthConfigured && (
                  <div className={cn(
                    "p-4 rounded-lg border",
                    isEmailDomainSupportedForOAuth(watchedEmail)
                      ? "bg-blue-50 border-blue-200"
                      : "bg-yellow-50 border-yellow-200"
                  )}>
                    <div className="flex items-start gap-3">
                      <Shield className={cn(
                        "mt-0.5",
                        isEmailDomainSupportedForOAuth(watchedEmail)
                          ? "text-blue-600"
                          : "text-yellow-600"
                      )} size={20} />
                      <div>
                        <h4 className={cn(
                          "font-medium",
                          isEmailDomainSupportedForOAuth(watchedEmail)
                            ? "text-blue-900"
                            : "text-yellow-900"
                        )}>
                          Microsoft OAuth 2.0 Authentication
                        </h4>
                        <p className={cn(
                          "text-sm mt-1",
                          isEmailDomainSupportedForOAuth(watchedEmail)
                            ? "text-blue-700"
                            : "text-yellow-700"
                        )}>
                          {isEmailDomainSupportedForOAuth(watchedEmail)
                            ? "Securely connect your Microsoft account without storing your password."
                            : "This email domain may not support OAuth. Consider using password authentication instead."
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* OAuth Error */}
                {oauthError && (
                  <div className="p-3 rounded-lg bg-red-50 border border-red-200">
                    <div className="flex items-start gap-2">
                      <AlertCircle className="text-red-600 mt-0.5" size={16} />
                      <div>
                        <p className="text-sm text-red-700">{oauthError}</p>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={clearOAuthError}
                          className="text-red-600 hover:text-red-700 p-0 h-auto mt-1"
                        >
                          Dismiss
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                {/* OAuth Success */}
                {oauthResult && (
                  <div className="p-3 rounded-lg bg-green-50 border border-green-200">
                    <div className="flex items-start gap-2">
                      <Shield className="text-green-600 mt-0.5" size={16} />
                      <div>
                        <p className="text-sm text-green-700 font-medium">
                          Authentication Successful
                        </p>
                        <p className="text-sm text-green-600">
                          Connected as: {oauthResult.account.name || oauthResult.account.username}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => startOAuth(watchedEmail)}
                  disabled={
                    !watchedEmail ||
                    !watchedEmail.includes('@') ||
                    isOAuthInProgress ||
                    !isOAuthConfigured ||
                    !!oauthResult
                  }
                >
                  {isOAuthInProgress ? (
                    <>
                      <RefreshCw size={16} className="mr-2 animate-spin" />
                      Authenticating...
                    </>
                  ) : oauthResult ? (
                    <>
                      <Shield size={16} className="mr-2" />
                      Authenticated ✓
                    </>
                  ) : (
                    <>
                      <Shield size={16} className="mr-2" />
                      Sign in with Microsoft
                    </>
                  )}
                </Button>

                {!watchedEmail && (
                  <p className="text-sm text-muted-foreground text-center">
                    Please enter your email address first
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Discovery Status & Manual Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Server Settings</h3>
                <Button type="button" variant="ghost" size="sm" onClick={handleManualDiscovery} disabled={discovery.isDiscovering || !watchedEmail}>
                    <RefreshCw size={14} className={cn(discovery.isDiscovering && 'animate-spin', 'mr-2')} />
                    Auto-Discover
                </Button>
            </div>

            {discovery.isDiscovering && (
                <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>Discovering email settings...</span>
                        <span className="text-xs">This may take up to 60 seconds</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{width: '100%'}}></div>
                    </div>
                </div>
            )}

            {discovery.discoveryStatus === 'failed' && (
                <div className="p-3 rounded-lg bg-yellow-50 border border-yellow-200 text-yellow-800">
                    <p className="text-sm">{discovery.discoveryMessage}</p>
                    <p className="text-xs mt-1">Please select a provider or enter settings manually.</p>
                </div>
            )}

            {showProviderSuggestions && (
                <Select onValueChange={(name) => handleProviderSelect(imapProviders.find(p => p.name === name)!.config)}>
                    <SelectTrigger><SelectValue placeholder="Select a common provider..." /></SelectTrigger>
                    <SelectContent>{imapProviders.map(p => <SelectItem key={p.name} value={p.name}>{p.name}</SelectItem>)}</SelectContent>
                </Select>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="incoming.protocol">Protocol</Label>
                    <Controller control={control} name="incoming.protocol" render={({ field }) => (
                        <ToggleGroup type="single" value={field.value} onValueChange={field.onChange} variant="outline" className="w-full">
                            <ToggleGroupItem value="imap" className="flex-1">IMAP</ToggleGroupItem>
                            <ToggleGroupItem value="pop3" className="flex-1">POP3</ToggleGroupItem>
                        </ToggleGroup>
                    )} />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="incoming.useTls">Security</Label>
                    <Controller control={control} name="incoming.useTls" render={({ field }) => (
                        <Button type="button" onClick={() => field.onChange(!field.value)} variant={field.value ? 'secondary' : 'outline'} className="w-full">
                            {field.value ? 'SSL/TLS Enabled' : 'No SSL/TLS'}
                        </Button>
                    )} />
                </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
                <div className="col-span-2">
                    <FormField
                      id="incoming.host"
                      label="Server Host"
                      value={watch('incoming.host') || ''}
                      onChange={(value) => setValue('incoming.host', value)}
                      error={errors.incoming?.host?.message}
                      placeholder="imap.example.com"
                    />
                </div>
                <div>
                    <FormField
                      id="incoming.port"
                      label="Port"
                      type="number"
                      value={watch('incoming.port')?.toString() || ''}
                      onChange={(value) => setValue('incoming.port', parseInt(value) || 993)}
                      error={errors.incoming?.port?.message}
                      placeholder="993"
                    />
                </div>
            </div>
          </div>

          {error && (
            <div className="p-3 rounded-lg bg-destructive/10 border border-destructive/20 text-destructive-foreground">
              <AlertCircle size={18} className="inline mr-2" /> {error}
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center gap-4 pt-4 border-t border-border">
            <Button type="submit" disabled={isSubmitting} className="flex-1">
              {isSubmitting ? <RefreshCw size={18} className="animate-spin" /> : <Save size={18} />}<span className="ml-2">{accountToEdit ? 'Save Changes' : 'Add Account'}</span>
            </Button>
            <Button type="button" variant="outline" onClick={onCancel}>Cancel</Button>
          </div>
        </form>
      </div>
    </div>
  );
};



export default AccountForm;