/**
 * @file Hook for handling OAuth authentication in forms
 */

import { useState, useCallback, useEffect } from 'react';
import type { OAuthResult } from '../../services/oauthService';

export interface UseOAuthFormReturn {
  isOAuthInProgress: boolean;
  oauthError: string | null;
  oauthResult: OAuthResult | null;
  startOAuth: (email: string) => Promise<void>;
  clearOAuthError: () => void;
  clearOAuthResult: () => void;
  isOAuthConfigured: boolean;
  oauthConfigIssues: string[];
}

/**
 * Hook for managing OAuth authentication flow in forms
 */
export function useOAuthForm(): UseOAuthFormReturn {
  const [isOAuthInProgress, setIsOAuthInProgress] = useState(false);
  const [oauthError, setOauthError] = useState<string | null>(null);
  const [oauthResult, setOauthResult] = useState<OAuthResult | null>(null);
  const [isOAuthConfigured, setIsOAuthConfigured] = useState(false);
  const [oauthConfigIssues, setOauthConfigIssues] = useState<string[]>([]);

  // Check OAuth configuration on hook initialization
  useEffect(() => {
    const checkConfig = async () => {
      try {
        const config = await window.ipcApi.oauth.checkConfig() as { isConfigured: boolean; issues: string[] };
        setIsOAuthConfigured(config.isConfigured);
        setOauthConfigIssues(config.issues);
      } catch (error) {
        console.error('Failed to check OAuth configuration:', error);
        setIsOAuthConfigured(false);
        setOauthConfigIssues(['Failed to check OAuth configuration']);
      }
    };

    checkConfig();
  }, []);

  const startOAuth = useCallback(async (email: string) => {
    if (!email || !email.includes('@')) {
      setOauthError('Please enter a valid email address');
      return;
    }

    // Validate OAuth configuration
    if (!isOAuthConfigured) {
      setOauthError('OAuth is not properly configured. Please check your environment variables.');
      return;
    }

    setIsOAuthInProgress(true);
    setOauthError(null);
    setOauthResult(null);

    try {
      console.log('Starting OAuth flow for email:', email);
      const result = await window.ipcApi.oauth.startFlow({ email }) as OAuthResult;

      console.log('OAuth flow completed successfully:', {
        username: result.account.username,
        hasAccessToken: !!result.accessToken,
        hasRefreshToken: !!result.refreshToken,
        expiresOn: result.expiresOn,
      });

      setOauthResult(result);
      setOauthError(null);

    } catch (error) {
      console.error('OAuth flow failed:', error);
      setOauthError(error instanceof Error ? error.message : 'OAuth authentication failed');
      setOauthResult(null);
    } finally {
      setIsOAuthInProgress(false);
    }
  }, [isOAuthConfigured]);

  const clearOAuthError = useCallback(() => {
    setOauthError(null);
  }, []);

  const clearOAuthResult = useCallback(() => {
    setOauthResult(null);
  }, []);

  return {
    isOAuthInProgress,
    oauthError,
    oauthResult,
    startOAuth,
    clearOAuthError,
    clearOAuthResult,
    isOAuthConfigured,
    oauthConfigIssues,
  };
}

/**
 * Helper function to check if an email domain supports OAuth
 * @param email The email address to check
 * @returns True if the domain is known to support Microsoft OAuth
 */
export function isEmailDomainSupportedForOAuth(email: string): boolean {
  if (!email || !email.includes('@')) {
    return false;
  }

  const domain = email.split('@')[1]?.toLowerCase();
  const supportedDomains = [
    'outlook.com',
    'hotmail.com',
    'live.com',
    'msn.com',
    // Add more Microsoft domains as needed
  ];

  return supportedDomains.includes(domain);
}

/**
 * Helper function to get OAuth status message for an email
 * @param email The email address
 * @returns Status message for OAuth support
 */
export function getOAuthStatusMessage(email: string): string {
  if (!email || !email.includes('@')) {
    return 'Enter an email address to check OAuth support';
  }

  if (isEmailDomainSupportedForOAuth(email)) {
    return 'This email domain supports OAuth 2.0 authentication';
  }

  return 'OAuth may not be supported for this email domain. Password authentication is recommended.';
}
