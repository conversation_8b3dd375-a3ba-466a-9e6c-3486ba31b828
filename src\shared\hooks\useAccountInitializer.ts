/**
 * @file Centralized hook for coordinated account initialization
 * Handles synchronized loading of mailboxes and initial emails to prevent race conditions
 */
import { useState, useEffect, useCallback, useRef } from 'react';

import { useAccountStore } from '../store/accounts/accountStore';
import { useLogStore } from '../store/logStore';
import type { EmailHeader } from '../types/email';
import type { MailBoxes } from '../types/electron';

interface AccountInitializationData {
  mailboxes: MailBoxes;
  defaultMailbox: string;
  initialEmails: EmailHeader[];
  totalEmailCount: number;
  mailboxCounts: Record<string, number>;
}

interface UseAccountInitializerReturn {
  // State
  isInitializing: boolean;
  initializationError: string | null;
  isInitialized: boolean;
  
  // Data
  mailboxes: MailBoxes | null;
  defaultMailbox: string | null;
  initialEmails: EmailHeader[];
  totalEmailCount: number;
  
  // Actions
  initializeAccount: (accountId: string, forceRefresh?: boolean) => Promise<void>;
  clearAccountData: (accountId: string) => void;
}

/**
 * Hook for coordinated account initialization
 * Ensures mailboxes and initial emails are loaded together to prevent UI inconsistencies
 */
export const useAccountInitializer = (): UseAccountInitializerReturn => {
  const {
    selectedAccountId,
    setMailboxesForAccount,
    selectMailbox,
    setEmailHeadersForMailbox,
    setEmailCountForMailbox,
    clearEmailHeadersForMailbox,
    mailboxesByAccountId,
    selectedMailbox,
    emailHeadersByMailbox,
    emailCountByMailbox,
  } = useAccountStore();

  const addLog = useLogStore((state) => state.addLog);

  // Local state
  const [isInitializing, setIsInitializing] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Cache to track initialized accounts
  const initializedAccounts = useRef(new Set<string>());
  
  // Get current account data
  const mailboxes = selectedAccountId ? mailboxesByAccountId[selectedAccountId] : null;
  const defaultMailbox = selectedMailbox;
  const emailKey = selectedAccountId && selectedMailbox ? `${selectedAccountId}-${selectedMailbox}` : null;
  const initialEmails = emailKey ? (emailHeadersByMailbox[emailKey] ?? []) : [];
  const totalEmailCount = emailKey ? (emailCountByMailbox[emailKey] ?? 0) : 0;

  /**
   * Initialize account data by fetching mailboxes and initial emails together
   */
  const initializeAccount = useCallback(async (accountId: string, forceRefresh: boolean = false): Promise<void> => {
    if (!accountId) {
      return;
    }

    // Skip if already initialized and not forcing refresh
    if (!forceRefresh && initializedAccounts.current.has(accountId)) {
      setIsInitialized(true);
      return;
    }

    setIsInitializing(true);
    setInitializationError(null);
    setIsInitialized(false);

    try {
      addLog(`Initializing account ${accountId}...`, 'info');

      // Call the new coordinated initialization API
      const result = await window.ipcApi.initializeAccount(accountId, 50) as AccountInitializationData;

      // Update stores with the coordinated data
      setMailboxesForAccount(accountId, result.mailboxes);

      // Select the default mailbox
      selectMailbox(result.defaultMailbox);

      // Set initial emails for the default mailbox
      setEmailHeadersForMailbox(accountId, result.defaultMailbox, result.initialEmails);
      setEmailCountForMailbox(accountId, result.defaultMailbox, result.totalEmailCount);

      // Set email counts for all mailboxes
      Object.entries(result.mailboxCounts).forEach(([mailboxName, count]) => {
        setEmailCountForMailbox(accountId, mailboxName, count);
      });

      // Mark as initialized
      initializedAccounts.current.add(accountId);
      setIsInitialized(true);

      addLog(`Account ${accountId} initialized successfully: ${Object.keys(result.mailboxes).length} mailboxes, ${result.initialEmails.length} emails from ${result.defaultMailbox}`, 'success');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize account';
      setInitializationError(errorMessage);
      addLog(`Failed to initialize account ${accountId}: ${errorMessage}`, 'error');
      
      // Remove from initialized cache on error
      initializedAccounts.current.delete(accountId);
    } finally {
      setIsInitializing(false);
    }
  }, [
    addLog,
    setMailboxesForAccount,
    selectMailbox,
    setEmailHeadersForMailbox,
    setEmailCountForMailbox,
  ]);

  /**
   * Clear account data from stores and cache
   */
  const clearAccountData = useCallback((accountId: string): void => {
    if (!accountId) return;

    // Clear from stores
    setMailboxesForAccount(accountId, null);
    
    // Clear all email data for this account
    const mailboxes = mailboxesByAccountId[accountId];
    if (mailboxes) {
      Object.keys(mailboxes).forEach(mailboxName => {
        clearEmailHeadersForMailbox(accountId, mailboxName);
      });
    }

    // Remove from initialized cache
    initializedAccounts.current.delete(accountId);
    setIsInitialized(false);
    setInitializationError(null);

    addLog(`Cleared data for account ${accountId}`, 'info');
  }, [
    setMailboxesForAccount,
    clearEmailHeadersForMailbox,
    mailboxesByAccountId,
    addLog,
  ]);

  // Auto-initialize when selected account changes
  useEffect(() => {
    if (selectedAccountId) {
      initializeAccount(selectedAccountId);
    } else {
      setIsInitialized(false);
      setInitializationError(null);
    }
  }, [selectedAccountId, initializeAccount]);

  return {
    // State
    isInitializing,
    initializationError,
    isInitialized,
    
    // Data
    mailboxes,
    defaultMailbox,
    initialEmails,
    totalEmailCount,
    
    // Actions
    initializeAccount,
    clearAccountData,
  };
};
