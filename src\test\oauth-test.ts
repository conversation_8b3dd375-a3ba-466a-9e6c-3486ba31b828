/**
 * @file OAuth integration test script
 * This script tests the OAuth implementation without requiring the full UI
 */

import { validateOAuthConfig, generateXOAUTH2String } from '../services/oauthService';
import { checkOAuthConfiguration, isOAuthAccountReady, validateOAuthAccount } from '../services/oauthImapService';
import { getTokenRefreshStatus } from '../services/tokenRefreshService';
import { listStoredTokenAccounts } from '../services/tokenStorageService';
import type { Account } from '../shared/types/account';

/**
 * Test OAuth configuration
 */
async function testOAuthConfiguration(): Promise<void> {
  console.log('\n=== Testing OAuth Configuration ===');
  
  try {
    // Check if OAuth is configured
    const isConfigured = validateOAuthConfig();
    console.log('✓ OAuth config validation:', isConfigured ? 'PASS' : 'FAIL');
    
    // Check detailed configuration
    const configDetails = checkOAuthConfiguration();
    console.log('✓ OAuth configuration details:');
    console.log('  - Is configured:', configDetails.isConfigured);
    console.log('  - Issues:', configDetails.issues.length > 0 ? configDetails.issues : 'None');
    
    // Check environment variables
    const hasClientId = !!process.env.MICROSOFT_CLIENT_ID;
    const clientIdLength = process.env.MICROSOFT_CLIENT_ID?.length || 0;
    console.log('✓ Environment check:');
    console.log('  - Has MICROSOFT_CLIENT_ID:', hasClientId);
    console.log('  - Client ID length:', clientIdLength);
    console.log('  - NODE_ENV:', process.env.NODE_ENV || 'not set');
    
  } catch (error) {
    console.error('✗ OAuth configuration test failed:', error);
  }
}

/**
 * Test OAuth account validation
 */
async function testAccountValidation(): Promise<void> {
  console.log('\n=== Testing Account Validation ===');
  
  try {
    // Test valid OAuth account
    const validOAuthAccount: Account = {
      id: 'test-oauth-account',
      email: '<EMAIL>',
      password: '',
      authMethod: 'oauth2',
      refreshToken: 'test-refresh-token',
      clientId: process.env.MICROSOFT_CLIENT_ID || '',
      displayName: 'Test OAuth Account',
      incoming: {
        protocol: 'imap',
        host: 'outlook.office365.com',
        port: 993,
        useTls: true,
      },
      connectionStatus: 'disconnected',
    };
    
    const validationErrors = validateOAuthAccount(validOAuthAccount);
    console.log('✓ Valid OAuth account validation:');
    console.log('  - Errors:', validationErrors.length > 0 ? validationErrors : 'None');
    
    const isReady = isOAuthAccountReady(validOAuthAccount);
    console.log('  - Is ready for OAuth:', isReady);
    
    // Test invalid OAuth account (missing refresh token)
    const invalidOAuthAccount: Account = {
      ...validOAuthAccount,
      refreshToken: undefined,
    };
    
    const invalidValidationErrors = validateOAuthAccount(invalidOAuthAccount);
    console.log('✓ Invalid OAuth account validation:');
    console.log('  - Errors:', invalidValidationErrors.length > 0 ? invalidValidationErrors : 'None');
    
    const invalidIsReady = isOAuthAccountReady(invalidOAuthAccount);
    console.log('  - Is ready for OAuth:', invalidIsReady);
    
    // Test password account
    const passwordAccount: Account = {
      ...validOAuthAccount,
      authMethod: 'password',
      password: 'test-password',
      refreshToken: undefined,
      clientId: undefined,
    };
    
    const passwordValidationErrors = validateOAuthAccount(passwordAccount);
    console.log('✓ Password account validation:');
    console.log('  - Errors:', passwordValidationErrors.length > 0 ? passwordValidationErrors : 'None');
    
  } catch (error) {
    console.error('✗ Account validation test failed:', error);
  }
}

/**
 * Test XOAUTH2 string generation
 */
async function testXOAUTH2Generation(): Promise<void> {
  console.log('\n=== Testing XOAUTH2 String Generation ===');
  
  try {
    const email = '<EMAIL>';
    const accessToken = 'test-access-token-12345';
    
    const xoauth2String = generateXOAUTH2String(email, accessToken);
    console.log('✓ XOAUTH2 string generated successfully');
    console.log('  - Length:', xoauth2String.length);
    console.log('  - Sample (first 50 chars):', xoauth2String.substring(0, 50) + '...');
    
    // Decode and verify format
    const decoded = Buffer.from(xoauth2String, 'base64').toString();
    const expectedFormat = `user=${email}\x01auth=Bearer ${accessToken}\x01\x01`;
    const isCorrectFormat = decoded === expectedFormat;
    
    console.log('  - Format validation:', isCorrectFormat ? 'PASS' : 'FAIL');
    if (!isCorrectFormat) {
      console.log('    Expected:', expectedFormat);
      console.log('    Actual:', decoded);
    }
    
  } catch (error) {
    console.error('✗ XOAUTH2 generation test failed:', error);
  }
}

/**
 * Test token storage
 */
async function testTokenStorage(): Promise<void> {
  console.log('\n=== Testing Token Storage ===');
  
  try {
    // List stored token accounts
    const storedAccounts = await listStoredTokenAccounts();
    console.log('✓ Stored token accounts:', storedAccounts.length);
    if (storedAccounts.length > 0) {
      console.log('  - Account IDs:', storedAccounts);
    }
    
    // Check token refresh service status
    const refreshStatus = getTokenRefreshStatus();
    console.log('✓ Token refresh service:');
    console.log('  - Is running:', refreshStatus.isRunning);
    console.log('  - Last check:', refreshStatus.lastCheck || 'Never');
    console.log('  - Account count:', refreshStatus.accountCount);
    
  } catch (error) {
    console.error('✗ Token storage test failed:', error);
  }
}

/**
 * Test email domain support
 */
async function testEmailDomainSupport(): Promise<void> {
  console.log('\n=== Testing Email Domain Support ===');
  
  const testEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>', // Should not be supported
    'invalid-email',   // Invalid format
  ];
  
  try {
    testEmails.forEach(email => {
      const domain = email.includes('@') ? email.split('@')[1]?.toLowerCase() : '';
      const supportedDomains = ['outlook.com', 'hotmail.com', 'live.com', 'msn.com'];
      const isSupported = supportedDomains.includes(domain);
      
      console.log(`✓ ${email}:`, isSupported ? 'Supported' : 'Not supported');
    });
    
  } catch (error) {
    console.error('✗ Email domain support test failed:', error);
  }
}

/**
 * Run all OAuth tests
 */
async function runOAuthTests(): Promise<void> {
  console.log('🚀 Starting OAuth Integration Tests');
  console.log('=====================================');
  
  await testOAuthConfiguration();
  await testAccountValidation();
  await testXOAUTH2Generation();
  await testTokenStorage();
  await testEmailDomainSupport();
  
  console.log('\n=====================================');
  console.log('✅ OAuth Integration Tests Complete');
  console.log('\nNext steps:');
  console.log('1. Ensure MICROSOFT_CLIENT_ID is set in .env file');
  console.log('2. Start the application with: npm run dev');
  console.log('3. Test OAuth flow with a real Microsoft account');
  console.log('4. Check IMAP connectivity after OAuth authentication');
}

/**
 * Export test functions for individual testing
 */
export {
  testOAuthConfiguration,
  testAccountValidation,
  testXOAUTH2Generation,
  testTokenStorage,
  testEmailDomainSupport,
  runOAuthTests,
};

// Run tests if this file is executed directly
if (require.main === module) {
  runOAuthTests().catch(console.error);
}
