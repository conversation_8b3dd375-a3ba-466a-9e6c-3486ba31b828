/**
 * @file OAuth IMAP integration service
 * Handles OAuth token management for IMAP connections
 */

import { refreshAccessToken, isTokenExpired, generateXOAUTH2String } from './oauthService';
import { getRefreshToken, storeRefreshToken } from './tokenStorageService';
import type { Account } from '../shared/types/account';

/**
 * Interface for OAuth IMAP authentication result
 */
export interface OAuthImapAuth {
  accessToken: string;
  xoauth2String: string;
}

/**
 * Cache for access tokens to avoid unnecessary refresh calls
 */
interface TokenCache {
  [accountId: string]: {
    accessToken: string;
    expiresOn: Date;
  };
}

const tokenCache: TokenCache = {};

/**
 * Get a valid access token for IMAP authentication
 * @param account The account to authenticate
 * @returns Promise that resolves with OAuth IMAP authentication data
 */
export async function getOAuthImapAuth(account: Account): Promise<OAuthImapAuth> {
  if (account.authMethod !== 'oauth2') {
    throw new Error('Account is not configured for OAuth authentication');
  }

  if (!account.refreshToken) {
    throw new Error('No refresh token available for OAuth account');
  }

  // Check if we have a cached token that's still valid
  const cached = tokenCache[account.id];
  if (cached && !isTokenExpired(cached.expiresOn)) {
    return {
      accessToken: cached.accessToken,
      xoauth2String: generateXOAUTH2String(account.email, cached.accessToken),
    };
  }

  try {
    // Refresh the access token
    const tokenResult = await refreshAccessToken(account.refreshToken, account.email);
    
    // Update cache
    tokenCache[account.id] = {
      accessToken: tokenResult.accessToken,
      expiresOn: tokenResult.expiresOn,
    };

    // If refresh token was rotated, store the new one
    if (tokenResult.refreshToken && tokenResult.refreshToken !== account.refreshToken) {
      await storeRefreshToken(account.id, tokenResult.refreshToken);
    }

    return {
      accessToken: tokenResult.accessToken,
      xoauth2String: generateXOAUTH2String(account.email, tokenResult.accessToken),
    };

  } catch (error) {
    // Clear cached token on error
    delete tokenCache[account.id];
    throw new Error(`Failed to get OAuth access token: ${error}`);
  }
}

/**
 * Clear cached tokens for an account
 * @param accountId The account ID to clear cache for
 */
export function clearTokenCache(accountId?: string): void {
  if (accountId) {
    delete tokenCache[accountId];
  } else {
    // Clear all cached tokens
    Object.keys(tokenCache).forEach(key => delete tokenCache[key]);
  }
}

/**
 * Check if an account is properly configured for OAuth
 * @param account The account to check
 * @returns True if account is ready for OAuth authentication
 */
export function isOAuthAccountReady(account: Account): boolean {
  return (
    account.authMethod === 'oauth2' &&
    !!account.refreshToken &&
    !!account.clientId &&
    account.email.includes('@')
  );
}

/**
 * Get OAuth authentication data for multiple accounts
 * @param accounts Array of accounts to authenticate
 * @returns Promise that resolves with authentication data for each account
 */
export async function getMultipleOAuthImapAuth(accounts: Account[]): Promise<Map<string, OAuthImapAuth>> {
  const results = new Map<string, OAuthImapAuth>();
  
  // Process accounts in parallel
  const promises = accounts
    .filter(account => account.authMethod === 'oauth2')
    .map(async (account) => {
      try {
        const auth = await getOAuthImapAuth(account);
        results.set(account.id, auth);
      } catch (error) {
        console.error(`Failed to get OAuth auth for account ${account.email}:`, error);
        // Don't add to results if authentication failed
      }
    });

  await Promise.all(promises);
  return results;
}

/**
 * Validate that an OAuth account has all required configuration
 * @param account The account to validate
 * @returns Array of validation errors (empty if valid)
 */
export function validateOAuthAccount(account: Account): string[] {
  const errors: string[] = [];

  if (account.authMethod !== 'oauth2') {
    errors.push('Account is not configured for OAuth authentication');
  }

  if (!account.refreshToken) {
    errors.push('No refresh token available');
  }

  if (!account.clientId) {
    errors.push('No client ID configured');
  }

  if (!account.email || !account.email.includes('@')) {
    errors.push('Invalid email address');
  }

  // Check if email domain supports OAuth (Microsoft domains)
  const domain = account.email.split('@')[1]?.toLowerCase();
  const supportedDomains = [
    'outlook.com',
    'hotmail.com',
    'live.com',
    'msn.com',
    // Add more Microsoft domains as needed
  ];

  if (domain && !supportedDomains.includes(domain)) {
    // For now, we only support Microsoft OAuth
    // In the future, this could be extended for other providers
    console.warn(`OAuth may not be supported for domain: ${domain}`);
  }

  return errors;
}

/**
 * Convert a password-based account to OAuth
 * This is a helper function for the UI to initiate OAuth setup
 * @param account The account to convert
 * @returns Account data ready for OAuth (without tokens - those come from the OAuth flow)
 */
export function prepareAccountForOAuth(account: Account): Omit<Account, 'id'> {
  return {
    ...account,
    authMethod: 'oauth2',
    password: '', // Clear password when switching to OAuth
    refreshToken: undefined, // Will be set after OAuth flow
    clientId: process.env.MICROSOFT_CLIENT_ID || '',
  };
}

/**
 * Check if OAuth is properly configured in the environment
 * @returns Object with configuration status and any issues
 */
export function checkOAuthConfiguration(): { isConfigured: boolean; issues: string[] } {
  const issues: string[] = [];
  
  const clientId = process.env.MICROSOFT_CLIENT_ID;
  if (!clientId || clientId === 'your-client-id-here') {
    issues.push('MICROSOFT_CLIENT_ID environment variable is not set or is using default value');
  }

  return {
    isConfigured: issues.length === 0,
    issues,
  };
}
