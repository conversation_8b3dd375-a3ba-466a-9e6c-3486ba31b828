/**
 * @file OAuth 2.0 authentication service for Microsoft accounts
 * Implements PKCE flow for secure desktop application authentication
 */

import { PublicClientApplication, Configuration, AuthenticationResult, SilentFlowRequest, AuthorizationUrlRequest } from '@azure/msal-node';
import { shell } from 'electron';
import crypto from 'crypto';

// Microsoft OAuth 2.0 configuration
const MICROSOFT_CLIENT_ID = process.env.MICROSOFT_CLIENT_ID || 'your-client-id-here';
const MICROSOFT_AUTHORITY = 'https://login.microsoftonline.com/common';
const REDIRECT_URI = 'http://localhost:3000/auth/callback';

// Required scopes for IMAP access
const SCOPES = [
  'https://outlook.office.com/IMAP.AccessAsUser.All',
  'offline_access', // Required for refresh tokens
  'openid',
  'profile',
  'email'
];

// MSAL configuration
const msalConfig: Configuration = {
  auth: {
    clientId: MICROSOFT_CLIENT_ID,
    authority: MICROSOFT_AUTHORITY,
  },
  cache: {
    cacheLocation: 'filesystem', // Use filesystem cache for desktop app
  },
};

let msalInstance: PublicClientApplication | null = null;

/**
 * Initialize the MSAL instance
 */
function getMsalInstance(): PublicClientApplication {
  if (!msalInstance) {
    msalInstance = new PublicClientApplication(msalConfig);
  }
  return msalInstance;
}

/**
 * Generate PKCE code verifier and challenge
 */
function generatePKCE() {
  const codeVerifier = crypto.randomBytes(32).toString('base64url');
  const codeChallenge = crypto.createHash('sha256').update(codeVerifier).digest('base64url');
  
  return {
    codeVerifier,
    codeChallenge,
    codeChallengeMethod: 'S256'
  };
}

/**
 * Interface for OAuth authentication result
 */
export interface OAuthResult {
  accessToken: string;
  refreshToken: string;
  expiresOn: Date;
  account: {
    username: string;
    name?: string;
  };
}

/**
 * Interface for token refresh result
 */
export interface TokenRefreshResult {
  accessToken: string;
  expiresOn: Date;
  refreshToken?: string; // New refresh token if rotated
}

/**
 * Start the OAuth 2.0 authentication flow
 * @param email Optional email hint for the user
 * @returns Promise that resolves with authentication result
 */
export async function startOAuthFlow(email?: string): Promise<OAuthResult> {
  const msal = getMsalInstance();
  const pkce = generatePKCE();
  
  // Build authorization URL request
  const authUrlRequest: AuthorizationUrlRequest = {
    scopes: SCOPES,
    redirectUri: REDIRECT_URI,
    codeChallenge: pkce.codeChallenge,
    codeChallengeMethod: pkce.codeChallengeMethod,
    loginHint: email,
    prompt: 'select_account', // Allow user to select account
  };

  try {
    // Get authorization URL
    const authUrl = await msal.getAuthCodeUrl(authUrlRequest);
    
    // Open browser for user authentication
    await shell.openExternal(authUrl);
    
    // Return a promise that will be resolved when the callback is received
    return new Promise((resolve, reject) => {
      // Set up a temporary HTTP server to handle the callback
      // This is a simplified implementation - in production, you'd want a more robust solution
      const http = require('http');
      const url = require('url');
      
      const server = http.createServer(async (req: any, res: any) => {
        const parsedUrl = url.parse(req.url, true);
        
        if (parsedUrl.pathname === '/auth/callback') {
          const { code, error } = parsedUrl.query;
          
          // Close the server
          server.close();
          
          if (error) {
            res.writeHead(400, { 'Content-Type': 'text/html' });
            res.end('<h1>Authentication Failed</h1><p>You can close this window.</p>');
            reject(new Error(`OAuth error: ${error}`));
            return;
          }
          
          if (!code) {
            res.writeHead(400, { 'Content-Type': 'text/html' });
            res.end('<h1>Authentication Failed</h1><p>No authorization code received.</p>');
            reject(new Error('No authorization code received'));
            return;
          }
          
          try {
            // Exchange authorization code for tokens
            const tokenRequest = {
              code: code as string,
              scopes: SCOPES,
              redirectUri: REDIRECT_URI,
              codeVerifier: pkce.codeVerifier,
            };
            
            const response = await msal.acquireTokenByCode(tokenRequest);
            
            if (!response) {
              throw new Error('No token response received');
            }
            
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end('<h1>Authentication Successful</h1><p>You can close this window and return to the application.</p>');
            
            // Return the authentication result
            resolve({
              accessToken: response.accessToken,
              refreshToken: response.refreshToken || '',
              expiresOn: response.expiresOn || new Date(Date.now() + 3600000), // 1 hour default
              account: {
                username: response.account?.username || email || '',
                name: response.account?.name,
              },
            });
            
          } catch (tokenError) {
            res.writeHead(500, { 'Content-Type': 'text/html' });
            res.end('<h1>Token Exchange Failed</h1><p>Please try again.</p>');
            reject(tokenError);
          }
        } else {
          res.writeHead(404, { 'Content-Type': 'text/html' });
          res.end('<h1>Not Found</h1>');
        }
      });
      
      // Start server on port 3000
      server.listen(3000, () => {
        console.log('OAuth callback server started on port 3000');
      });
      
      // Set timeout for the authentication process
      setTimeout(() => {
        server.close();
        reject(new Error('Authentication timeout'));
      }, 300000); // 5 minutes timeout
    });
    
  } catch (error) {
    console.error('OAuth flow error:', error);
    throw new Error(`Failed to start OAuth flow: ${error}`);
  }
}

/**
 * Refresh an access token using a refresh token
 * @param refreshToken The refresh token
 * @param accountUsername The account username/email
 * @returns Promise that resolves with new tokens
 */
export async function refreshAccessToken(refreshToken: string, accountUsername: string): Promise<TokenRefreshResult> {
  const msal = getMsalInstance();
  
  try {
    const silentRequest: SilentFlowRequest = {
      scopes: SCOPES,
      account: {
        homeAccountId: '',
        environment: 'login.microsoftonline.com',
        tenantId: 'common',
        username: accountUsername,
        localAccountId: '',
      },
      forceRefresh: true, // Force refresh to get new token
    };
    
    const response = await msal.acquireTokenSilent(silentRequest);
    
    if (!response) {
      throw new Error('No token response received');
    }
    
    return {
      accessToken: response.accessToken,
      expiresOn: response.expiresOn || new Date(Date.now() + 3600000),
      refreshToken: response.refreshToken, // May be rotated
    };
    
  } catch (error) {
    console.error('Token refresh error:', error);
    throw new Error(`Failed to refresh token: ${error}`);
  }
}

/**
 * Generate XOAUTH2 authentication string for IMAP
 * @param email The user's email address
 * @param accessToken The OAuth access token
 * @returns Base64-encoded XOAUTH2 string
 */
export function generateXOAUTH2String(email: string, accessToken: string): string {
  const authString = `user=${email}\x01auth=Bearer ${accessToken}\x01\x01`;
  return Buffer.from(authString).toString('base64');
}

/**
 * Check if an access token is expired or about to expire
 * @param expiresOn The token expiration date
 * @param bufferMinutes Buffer time in minutes before considering token expired
 * @returns True if token needs refresh
 */
export function isTokenExpired(expiresOn: Date, bufferMinutes: number = 5): boolean {
  const now = new Date();
  const bufferTime = bufferMinutes * 60 * 1000; // Convert to milliseconds
  return (expiresOn.getTime() - now.getTime()) <= bufferTime;
}

/**
 * Validate Microsoft OAuth configuration
 * @returns True if configuration is valid
 */
export function validateOAuthConfig(): boolean {
  return MICROSOFT_CLIENT_ID !== 'your-client-id-here' && MICROSOFT_CLIENT_ID.length > 0;
}
