# OAuth 2.0 Setup Guide for Microsoft Accounts

This guide explains how to set up and test OAuth 2.0 authentication for Microsoft accounts (Outlook, Hotmail, Live) in the IMAP Viewer application.

## Prerequisites

1. **Azure App Registration**: You need to register an application in Azure Active Directory
2. **Microsoft Account**: A Microsoft account for testing (Outlook, Hotmail, Live, etc.)
3. **Environment Configuration**: Proper environment variables setup

## Azure App Registration

### Step 1: Create Azure App Registration

1. Go to [Azure Portal](https://portal.azure.com/)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Click **New registration**
4. Fill in the details:
   - **Name**: `IMAP Viewer OAuth`
   - **Supported account types**: `Accounts in any organizational directory and personal Microsoft accounts`
   - **Redirect URI**: 
     - Platform: `Web`
     - URI: `http://localhost:3000/auth/callback`
5. Click **Register**

### Step 2: Configure API Permissions

1. In your app registration, go to **API permissions**
2. Click **Add a permission**
3. Select **Microsoft Graph**
4. Choose **Delegated permissions**
5. Add these permissions:
   - `https://outlook.office.com/IMAP.AccessAsUser.All`
   - `offline_access`
   - `openid`
   - `profile`
   - `email`
6. Click **Grant admin consent** (if you're an admin)

### Step 3: Get Client ID

1. Go to **Overview** in your app registration
2. Copy the **Application (client) ID**
3. This will be your `MICROSOFT_CLIENT_ID`

## Environment Configuration

Create a `.env` file in the root directory of your project:

```env
# Microsoft OAuth Configuration
MICROSOFT_CLIENT_ID=your-application-client-id-here

# Optional: Development settings
NODE_ENV=development
```

**Important**: Replace `your-application-client-id-here` with your actual Azure app registration client ID.

## Testing OAuth Integration

### 1. Start the Application

```bash
npm run dev
```

### 2. Test OAuth Configuration

1. Open the application
2. Go to **Account Manager**
3. Click **Add New Account**
4. Enter a Microsoft email address (e.g., `<EMAIL>`)
5. Select **OAuth 2.0** as the authentication method
6. The UI should show OAuth configuration status

### 3. Test OAuth Flow

1. Click **Sign in with Microsoft**
2. A browser window should open with Microsoft login
3. Sign in with your Microsoft account
4. Grant permissions to the application
5. The browser should show "Authentication Successful"
6. Return to the application - it should show authentication success

### 4. Test IMAP Connection

1. After successful OAuth authentication, complete the account setup
2. The application should automatically discover IMAP settings
3. Save the account
4. Test IMAP connection by viewing mailboxes and emails

## Troubleshooting

### Common Issues

#### 1. "OAuth is not properly configured"
- **Cause**: Missing or invalid `MICROSOFT_CLIENT_ID`
- **Solution**: Check your `.env` file and Azure app registration

#### 2. "Authentication failed"
- **Cause**: Incorrect redirect URI or permissions
- **Solution**: Verify redirect URI in Azure matches `http://localhost:3000/auth/callback`

#### 3. "IMAP connection failed"
- **Cause**: Token refresh issues or IMAP server problems
- **Solution**: Check token storage and refresh mechanism

#### 4. Browser doesn't open
- **Cause**: System browser configuration
- **Solution**: Manually copy the OAuth URL from console logs

### Debug Information

You can check OAuth status using the developer tools:

```javascript
// Check OAuth configuration
await window.ipcApi.oauth.checkConfig()

// Get environment info
await window.ipcApi.oauth.getEnvInfo()

// Test configuration
await window.ipcApi.oauth.testConfig()

// Check refresh service status
await window.ipcApi.oauth.getRefreshStatus()
```

## Security Considerations

1. **Client ID**: The client ID is not secret for public clients (desktop apps)
2. **Refresh Tokens**: Stored securely using OS keychain (keytar) with encrypted fallback
3. **Access Tokens**: Never stored persistently, only cached in memory
4. **PKCE**: Uses Proof Key for Code Exchange for additional security

## Supported Email Domains

The OAuth implementation currently supports these Microsoft domains:
- `outlook.com`
- `hotmail.com`
- `live.com`
- `msn.com`

Other domains may work but are not officially tested.

## Token Management

### Automatic Token Refresh

- Tokens are automatically refreshed before expiration
- Background service runs every 5 minutes to check token status
- Failed refresh attempts are logged and retried

### Manual Token Management

```javascript
// List accounts with stored tokens
await window.ipcApi.oauth.listTokenAccounts()

// Manually refresh all tokens
await window.ipcApi.oauth.refreshAllTokens()

// Remove token for specific account
await window.ipcApi.oauth.removeRefreshToken(accountId)

// Clear all tokens (for reset)
await window.ipcApi.oauth.clearAllTokens()
```

## Development Notes

### File Structure

- `src/services/oauthService.ts` - Core OAuth implementation
- `src/services/oauthImapService.ts` - OAuth-IMAP integration
- `src/services/tokenStorageService.ts` - Secure token storage
- `src/services/tokenRefreshService.ts` - Background token refresh
- `src/ipc/oauth.ts` - IPC handlers for OAuth
- `src/shared/hooks/useOAuthForm.ts` - React hook for OAuth UI

### Testing Different Scenarios

1. **First-time OAuth**: Test with new Microsoft account
2. **Token Refresh**: Wait for automatic refresh or trigger manually
3. **Token Expiry**: Test behavior when tokens expire
4. **Network Issues**: Test offline/online scenarios
5. **Multiple Accounts**: Test with multiple Microsoft accounts

## Production Deployment

For production deployment:

1. Update redirect URI to production domain
2. Configure proper environment variables
3. Ensure secure token storage is working
4. Test with real Microsoft accounts
5. Monitor token refresh logs

## Support

If you encounter issues:

1. Check the console logs for detailed error messages
2. Verify Azure app registration configuration
3. Test with different Microsoft accounts
4. Check network connectivity and firewall settings
5. Review the troubleshooting section above
